minimapWidget = nil
minimapWindow = nil
otmm = true
preloaded = false
fullmapView = false
oldZoom = nil
oldPos = nil

function init()
  minimapWindow = g_ui.loadUI('minimap')
  minimapWindow:setContentMinimumHeight(64)

  minimapWidget = minimapWindow:recursiveGetChildById('minimap')

  local gameRootPanel = modules.game_interface.getRootPanel()
  g_keyboard.bindKeyPress('Alt+Left', function() minimapWidget:move(1,0) end, gameRootPanel)
  g_keyboard.bindKeyPress('Alt+Right', function() minimapWidget:move(-1,0) end, gameRootPanel)
  g_keyboard.bindKeyPress('Alt+Up', function() minimapWidget:move(0,1) end, gameRootPanel)
  g_keyboard.bindKeyPress('Alt+Down', function() minimapWidget:move(0,-1) end, gameRootPanel)
  g_keyboard.bindKeyDown('Ctrl+M', toggle)
  g_keyboard.bindKeyDown('Ctrl+Shift+M', toggleFullMap)

  minimapWindow:setup()

  connect(g_game, {
    onGameStart = online,
    onGameEnd = offline,
  })

  connect(LocalPlayer, {
    onPositionChange = updateCameraPosition
  })

  if g_game.isOnline() then
    online()
  end
end

function terminate()
  if g_game.isOnline() then
    saveMap()
  end

  disconnect(g_game, {
    onGameStart = online,
    onGameEnd = offline,
  })

  disconnect(LocalPlayer, {
    onPositionChange = updateCameraPosition
  })

  local gameRootPanel = modules.game_interface.getRootPanel()
  g_keyboard.unbindKeyPress('Alt+Left', gameRootPanel)
  g_keyboard.unbindKeyPress('Alt+Right', gameRootPanel)
  g_keyboard.unbindKeyPress('Alt+Up', gameRootPanel)
  g_keyboard.unbindKeyPress('Alt+Down', gameRootPanel)
  g_keyboard.unbindKeyDown('Ctrl+M')
  g_keyboard.unbindKeyDown('Ctrl+Shift+M')

  minimapWindow:destroy()
end

function toggle()
  if minimapWindow:isVisible() then
    minimapWindow:close()
  else
    minimapWindow:open()
  end
end

function onMiniWindowClose()
  -- No action needed here anymore as the button is gone.
end

function preload()
  loadMap(false)
  preloaded = true
end

function online()
  loadMap(not preloaded)
  updateCameraPosition()
end

function offline()
  saveMap()
end

function loadMap(clean)
  local clientVersion = g_game.getClientVersion()

  if clean then
    g_minimap.clean()
  end

  if otmm then
    local minimapFile = '/minimap.otmm'
    print("Trying to load OTMM file: " .. minimapFile)

    if g_resources.fileExists('/data' .. minimapFile) then
      print("Found OTMM file at: /data" .. minimapFile)
      g_minimap.loadOtmm('/data' .. minimapFile)
    elseif g_resources.fileExists(minimapFile) then
      print("Found OTMM file at: " .. minimapFile)
      g_minimap.loadOtmm(minimapFile)
    else
      print("No OTMM file found, trying PNG files...")
      loadPngMinimap()
    end
  else
    local minimapFile = '/minimap_' .. clientVersion .. '.otcm'
    if g_resources.fileExists('/data' .. minimapFile) then
      g_map.loadOtcm('/data' .. minimapFile)
    elseif g_resources.fileExists(minimapFile) then
      g_map.loadOtcm(minimapFile)
    end
  end
  minimapWidget:load()
end

function saveMap()
  local clientVersion = g_game.getClientVersion()
  if otmm then
    local minimapFile = '/minimap.otmm'
    g_minimap.saveOtmm(minimapFile)
  else
    local minimapFile = '/minimap_' .. clientVersion .. '.otcm'
    g_map.saveOtcm(minimapFile)
  end
  minimapWidget:save()
end

function updateCameraPosition()
  local player = g_game.getLocalPlayer()
  if not player then return end
  local pos = player:getPosition()
  if not pos then return end
  if not minimapWidget:isDragging() then
    --if not fullmapView then
      minimapWidget:setCameraPosition(player:getPosition())
    --end
    minimapWidget:setCrossPosition(player:getPosition())
  end
end

function loadPngMinimap()
  -- Function to load PNG minimap files
  print("Loading PNG minimap files...")

  local loadedCount = 0

  -- Load PNG files based on your coordinate range
  for x = 147, 172 do
    for y = 124, 139 do
      for z = 0, 8 do
        local filename = '/data/minimap/' .. x .. '_' .. y .. '_' .. z .. '.png'
        if g_resources.fileExists(filename) then
          -- Try different coordinate calculations
          -- Option 1: Direct coordinate mapping
          local topLeft = {x = x * 256, y = y * 256, z = z}

          -- Try to load the image
          local success = pcall(function()
            g_minimap.loadImage(filename, topLeft, 1.0)
          end)

          if success then
            print("Loaded: " .. filename .. " at (" .. topLeft.x .. "," .. topLeft.y .. "," .. topLeft.z .. ")")
            loadedCount = loadedCount + 1
          else
            print("Failed to load: " .. filename)
          end
        end
      end
    end
  end

  if loadedCount > 0 then
    print("PNG minimap loading complete! Loaded " .. loadedCount .. " files.")
  else
    print("No PNG minimap files were loaded. Check file paths and format.")
  end
end

function toggleFullMap()
  if not fullmapView then
    fullmapView = true
    minimapWindow:hide()
    minimapWidget:setParent(modules.game_interface.getRootPanel())
    minimapWidget:fill('parent')
    minimapWidget:setAlternativeWidgetsVisible(true)
  else
    fullmapView = false
    minimapWidget:setParent(minimapWindow:getChildById('contentsPanel'))
    minimapWidget:fill('parent')
    minimapWindow:show()
    minimapWidget:setAlternativeWidgetsVisible(false)
  end

  local zoom = oldZoom or 0
  local pos = oldPos or minimapWidget:getCameraPosition()
  oldZoom = minimapWidget:getZoom()
  oldPos = minimapWidget:getCameraPosition()
  minimapWidget:setZoom(zoom)
  minimapWidget:setCameraPosition(minimapWidget:getCameraPosition())
end
